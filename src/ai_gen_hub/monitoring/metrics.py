"""
AI Gen Hub 指标收集系统

提供Prometheus指标收集功能，包括：
- 请求指标（QPS、响应时间、错误率）
- 供应商指标（可用性、延迟、成功率）
- 缓存指标（命中率、内存使用）
- 系统指标（CPU、内存、网络）
"""

import time
from typing import Dict, Optional

from prometheus_client import (
    Counter,
    Gauge,
    Histogram,
    Info,
    generate_latest,
    CONTENT_TYPE_LATEST,
)

from ai_gen_hub.core.logging import LoggerMixin


class MetricsCollector(LoggerMixin):
    """指标收集器"""
    
    def __init__(self, app_name: str = "ai_gen_hub"):
        """初始化指标收集器
        
        Args:
            app_name: 应用名称
        """
        self.app_name = app_name
        
        # 应用信息
        self.app_info = Info(
            'ai_gen_hub_info',
            '应用信息',
            ['version', 'environment']
        )
        
        # 请求指标
        self.request_total = Counter(
            'ai_gen_hub_requests_total',
            '总请求数',
            ['provider', 'model', 'request_type', 'status']
        )
        
        self.request_duration = Histogram(
            'ai_gen_hub_request_duration_seconds',
            '请求持续时间（秒）',
            ['provider', 'model', 'request_type'],
            buckets=[0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0, 60.0, 120.0, float('inf')]
        )
        
        self.request_size = Histogram(
            'ai_gen_hub_request_size_bytes',
            '请求大小（字节）',
            ['provider', 'model', 'request_type'],
            buckets=[100, 1000, 10000, 100000, 1000000, float('inf')]
        )
        
        self.response_size = Histogram(
            'ai_gen_hub_response_size_bytes',
            '响应大小（字节）',
            ['provider', 'model', 'request_type'],
            buckets=[100, 1000, 10000, 100000, 1000000, float('inf')]
        )
        
        # 供应商指标
        self.provider_availability = Gauge(
            'ai_gen_hub_provider_availability',
            '供应商可用性（0-1）',
            ['provider']
        )
        
        self.provider_response_time = Gauge(
            'ai_gen_hub_provider_avg_response_time_seconds',
            '供应商平均响应时间（秒）',
            ['provider']
        )
        
        self.provider_error_rate = Gauge(
            'ai_gen_hub_provider_error_rate',
            '供应商错误率（0-1）',
            ['provider']
        )
        
        self.provider_active_connections = Gauge(
            'ai_gen_hub_provider_active_connections',
            '供应商活跃连接数',
            ['provider']
        )
        
        # 密钥池指标
        self.key_pool_total = Gauge(
            'ai_gen_hub_key_pool_total',
            '密钥池总数',
            ['provider']
        )
        
        self.key_pool_active = Gauge(
            'ai_gen_hub_key_pool_active',
            '密钥池活跃数',
            ['provider']
        )
        
        self.key_usage_count = Counter(
            'ai_gen_hub_key_usage_total',
            '密钥使用次数',
            ['provider', 'key_id', 'status']
        )
        
        # 缓存指标
        self.cache_hits = Counter(
            'ai_gen_hub_cache_hits_total',
            '缓存命中次数',
            ['cache_type', 'cache_level']
        )
        
        self.cache_misses = Counter(
            'ai_gen_hub_cache_misses_total',
            '缓存未命中次数',
            ['cache_type', 'cache_level']
        )
        
        self.cache_size = Gauge(
            'ai_gen_hub_cache_size_bytes',
            '缓存大小（字节）',
            ['cache_type', 'cache_level']
        )
        
        self.cache_items = Gauge(
            'ai_gen_hub_cache_items_total',
            '缓存项目数量',
            ['cache_type', 'cache_level']
        )
        
        # 熔断器指标
        self.circuit_breaker_state = Gauge(
            'ai_gen_hub_circuit_breaker_state',
            '熔断器状态（0=关闭，1=半开，2=打开）',
            ['provider', 'circuit_breaker']
        )
        
        self.circuit_breaker_failures = Counter(
            'ai_gen_hub_circuit_breaker_failures_total',
            '熔断器失败次数',
            ['provider', 'circuit_breaker']
        )
        
        # 系统指标
        self.active_requests = Gauge(
            'ai_gen_hub_active_requests',
            '当前活跃请求数'
        )
        
        self.queue_size = Gauge(
            'ai_gen_hub_queue_size',
            '队列大小',
            ['queue_type']
        )
        
        # Token使用指标
        self.tokens_consumed = Counter(
            'ai_gen_hub_tokens_consumed_total',
            '消耗的Token数量',
            ['provider', 'model', 'token_type']
        )
        
        self.estimated_cost = Counter(
            'ai_gen_hub_estimated_cost_total',
            '估算成本',
            ['provider', 'model', 'currency']
        )
        
        self.logger.info("指标收集器初始化完成")
    
    def set_app_info(self, version: str, environment: str) -> None:
        """设置应用信息"""
        self.app_info.info({
            'version': version,
            'environment': environment
        })
    
    def record_request(
        self,
        provider: str,
        model: str,
        request_type: str,
        duration: float,
        status: str,
        request_size: Optional[int] = None,
        response_size: Optional[int] = None
    ) -> None:
        """记录请求指标
        
        Args:
            provider: 供应商名称
            model: 模型名称
            request_type: 请求类型
            duration: 请求持续时间
            status: 请求状态（success, error, timeout等）
            request_size: 请求大小
            response_size: 响应大小
        """
        labels = {
            'provider': provider,
            'model': model,
            'request_type': request_type
        }
        
        # 记录请求计数
        self.request_total.labels(**labels, status=status).inc()
        
        # 记录请求持续时间
        self.request_duration.labels(**labels).observe(duration)
        
        # 记录请求和响应大小
        if request_size is not None:
            self.request_size.labels(**labels).observe(request_size)
        
        if response_size is not None:
            self.response_size.labels(**labels).observe(response_size)
    
    def update_provider_metrics(
        self,
        provider: str,
        availability: float,
        avg_response_time: float,
        error_rate: float,
        active_connections: int
    ) -> None:
        """更新供应商指标
        
        Args:
            provider: 供应商名称
            availability: 可用性（0-1）
            avg_response_time: 平均响应时间
            error_rate: 错误率（0-1）
            active_connections: 活跃连接数
        """
        self.provider_availability.labels(provider=provider).set(availability)
        self.provider_response_time.labels(provider=provider).set(avg_response_time)
        self.provider_error_rate.labels(provider=provider).set(error_rate)
        self.provider_active_connections.labels(provider=provider).set(active_connections)
    
    def update_key_pool_metrics(
        self,
        provider: str,
        total_keys: int,
        active_keys: int
    ) -> None:
        """更新密钥池指标
        
        Args:
            provider: 供应商名称
            total_keys: 总密钥数
            active_keys: 活跃密钥数
        """
        self.key_pool_total.labels(provider=provider).set(total_keys)
        self.key_pool_active.labels(provider=provider).set(active_keys)
    
    def record_key_usage(
        self,
        provider: str,
        key_id: str,
        status: str
    ) -> None:
        """记录密钥使用
        
        Args:
            provider: 供应商名称
            key_id: 密钥ID（脱敏后）
            status: 使用状态
        """
        self.key_usage_count.labels(
            provider=provider,
            key_id=key_id,
            status=status
        ).inc()
    
    def update_cache_metrics(
        self,
        cache_type: str,
        cache_level: str,
        hits: int,
        misses: int,
        size: int,
        items: int
    ) -> None:
        """更新缓存指标
        
        Args:
            cache_type: 缓存类型
            cache_level: 缓存级别
            hits: 命中次数
            misses: 未命中次数
            size: 缓存大小
            items: 缓存项目数
        """
        labels = {'cache_type': cache_type, 'cache_level': cache_level}
        
        # 设置当前值（不是累加）
        self.cache_hits.labels(**labels)._value._value = hits
        self.cache_misses.labels(**labels)._value._value = misses
        self.cache_size.labels(**labels).set(size)
        self.cache_items.labels(**labels).set(items)
    
    def record_cache_operation(
        self,
        cache_type: str,
        cache_level: str,
        operation: str  # hit, miss
    ) -> None:
        """记录缓存操作
        
        Args:
            cache_type: 缓存类型
            cache_level: 缓存级别
            operation: 操作类型
        """
        labels = {'cache_type': cache_type, 'cache_level': cache_level}
        
        if operation == 'hit':
            self.cache_hits.labels(**labels).inc()
        elif operation == 'miss':
            self.cache_misses.labels(**labels).inc()
    
    def update_circuit_breaker_metrics(
        self,
        provider: str,
        circuit_breaker: str,
        state: str,
        failure_count: int
    ) -> None:
        """更新熔断器指标
        
        Args:
            provider: 供应商名称
            circuit_breaker: 熔断器名称
            state: 熔断器状态
            failure_count: 失败次数
        """
        # 状态映射
        state_mapping = {
            'closed': 0,
            'half_open': 1,
            'open': 2
        }
        
        state_value = state_mapping.get(state.lower(), 0)
        
        self.circuit_breaker_state.labels(
            provider=provider,
            circuit_breaker=circuit_breaker
        ).set(state_value)
        
        self.circuit_breaker_failures.labels(
            provider=provider,
            circuit_breaker=circuit_breaker
        )._value._value = failure_count
    
    def update_system_metrics(
        self,
        active_requests: int,
        queue_sizes: Dict[str, int]
    ) -> None:
        """更新系统指标
        
        Args:
            active_requests: 活跃请求数
            queue_sizes: 队列大小字典
        """
        self.active_requests.set(active_requests)
        
        for queue_type, size in queue_sizes.items():
            self.queue_size.labels(queue_type=queue_type).set(size)
    
    def record_token_usage(
        self,
        provider: str,
        model: str,
        token_type: str,  # input, output
        count: int
    ) -> None:
        """记录Token使用
        
        Args:
            provider: 供应商名称
            model: 模型名称
            token_type: Token类型
            count: Token数量
        """
        self.tokens_consumed.labels(
            provider=provider,
            model=model,
            token_type=token_type
        ).inc(count)
    
    def record_cost(
        self,
        provider: str,
        model: str,
        cost: float,
        currency: str = "USD"
    ) -> None:
        """记录成本
        
        Args:
            provider: 供应商名称
            model: 模型名称
            cost: 成本
            currency: 货币
        """
        self.estimated_cost.labels(
            provider=provider,
            model=model,
            currency=currency
        ).inc(cost)
    
    def get_metrics(self) -> str:
        """获取Prometheus格式的指标数据
        
        Returns:
            Prometheus格式的指标字符串
        """
        return generate_latest().decode('utf-8')
    
    def get_content_type(self) -> str:
        """获取指标内容类型
        
        Returns:
            内容类型
        """
        return CONTENT_TYPE_LATEST


# 全局指标收集器实例
metrics_collector = MetricsCollector()
